import { motion, AnimatePresence } from "framer-motion";
import { AlertTriangle, ChevronDown, ChevronUp, X } from "lucide-react";
import React, { useState } from "react";
import { Button } from "@/shared/components/ui/button";

interface ErrorSummaryProps {
	totalErrors: number;
	itemsWithErrors: number;
	errorsByItem: Array<{
		index: number;
		errorCount: number;
		errorFields: string[];
	}>;
	onScrollToItem: (itemIndex: number) => void;
	onDismiss?: () => void;
}

export const ErrorSummary: React.FC<ErrorSummaryProps> = ({
	totalErrors,
	itemsWithErrors,
	errorsByItem,
	onScrollToItem,
	onDismiss,
}) => {
	const [isExpanded, setIsExpanded] = useState(false);

	if (totalErrors === 0) {
		return null;
	}

	const getFieldDisplayName = (fieldPath: string): string => {
		const fieldMappings: Record<string, string> = {
			"stockMovement.product.name": "Nome do Produto",
			"stockMovement.product.barcode": "Código de Barras",
			"stockMovement.product.costPrice": "Preço de Custo",
			"stockMovement.product.price": "Preço de Venda",
			"stockMovement.product.ncm": "NCM",
			"stockMovement.quantity": "Quantidade",
			"stockMovement.product.categoryId": "Categoria",
			"stockMovement.product.package.name": "Nome da Embalagem",
			"stockMovement.product.package.barcode": "Código de Barras da Embalagem",
			"stockMovement.product.package.code": "Código da Embalagem",
			"stockMovement.product.package.quantityPerPackage": "Quantidade por Embalagem",
			"expirationDate": "Data de Validade",
		};

		return fieldMappings[fieldPath] || fieldPath;
	};

	return (
		<AnimatePresence>
			<motion.div
				initial={{ opacity: 0, y: -20, height: 0 }}
				animate={{ opacity: 1, y: 0, height: "auto" }}
				exit={{ opacity: 0, y: -20, height: 0 }}
				transition={{ duration: 0.3 }}
				className="mb-6 border border-red-200 rounded-xl bg-red-50 overflow-hidden"
			>
				{/* Header do resumo */}
				<div className="p-4 bg-gradient-to-r from-red-100 to-orange-100">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-3">
							<div className="p-2 bg-red-200 rounded-lg">
								<AlertTriangle className="w-5 h-5 text-red-600" />
							</div>
							<div>
								<h3 className="text-lg font-semibold text-red-800">
									{totalErrors} erro{totalErrors !== 1 ? "s" : ""} encontrado{totalErrors !== 1 ? "s" : ""}
								</h3>
								<p className="text-sm text-red-600">
									{itemsWithErrors} item{itemsWithErrors !== 1 ? "s" : ""} com problema{itemsWithErrors !== 1 ? "s" : ""}
								</p>
							</div>
						</div>
						<div className="flex items-center gap-2">
							<Button
								variant="ghost"
								size="sm"
								onClick={() => setIsExpanded(!isExpanded)}
								className="text-red-700 hover:text-red-800 hover:bg-red-200"
							>
								{isExpanded ? (
									<>
										<ChevronUp className="w-4 h-4 mr-1" />
										Ocultar detalhes
									</>
								) : (
									<>
										<ChevronDown className="w-4 h-4 mr-1" />
										Ver detalhes
									</>
								)}
							</Button>
							{onDismiss && (
								<Button
									variant="ghost"
									size="sm"
									onClick={onDismiss}
									className="text-red-700 hover:text-red-800 hover:bg-red-200"
								>
									<X className="w-4 h-4" />
								</Button>
							)}
						</div>
					</div>
				</div>

				{/* Lista detalhada de erros */}
				<AnimatePresence>
					{isExpanded && (
						<motion.div
							initial={{ opacity: 0, height: 0 }}
							animate={{ opacity: 1, height: "auto" }}
							exit={{ opacity: 0, height: 0 }}
							transition={{ duration: 0.2 }}
							className="border-t border-red-200"
						>
							<div className="p-4 space-y-3 max-h-60 overflow-y-auto">
								{errorsByItem.map((item) => (
									<motion.div
										key={item.index}
										initial={{ opacity: 0, x: -10 }}
										animate={{ opacity: 1, x: 0 }}
										transition={{ delay: item.index * 0.05 }}
										className="flex items-start justify-between p-3 bg-white rounded-lg border border-red-100 hover:border-red-200 transition-colors"
									>
										<div className="flex-1">
											<div className="flex items-center gap-2 mb-2">
												<span className="text-sm font-medium text-red-800">
													Item {item.index + 1}
												</span>
												<span className="px-2 py-1 text-xs bg-red-200 text-red-700 rounded-full">
													{item.errorCount} erro{item.errorCount !== 1 ? "s" : ""}
												</span>
											</div>
											<div className="space-y-1">
												{item.errorFields.slice(0, 3).map((field, fieldIndex) => (
													<div key={fieldIndex} className="text-xs text-red-600">
														• {getFieldDisplayName(field)}
													</div>
												))}
												{item.errorFields.length > 3 && (
													<div className="text-xs text-red-500 italic">
														... e mais {item.errorFields.length - 3} campo{item.errorFields.length - 3 !== 1 ? "s" : ""}
													</div>
												)}
											</div>
										</div>
										<Button
											variant="outline"
											size="sm"
											onClick={() => onScrollToItem(item.index)}
											className="ml-3 text-red-700 border-red-300 hover:bg-red-100 hover:border-red-400"
										>
											Ir para item
										</Button>
									</motion.div>
								))}
							</div>
						</motion.div>
					)}
				</AnimatePresence>

				{/* Ações rápidas */}
				{!isExpanded && errorsByItem.length > 0 && (
					<div className="p-3 bg-red-50 border-t border-red-200">
						<div className="flex items-center justify-between">
							<span className="text-sm text-red-600">
								Primeiro item com erro: Item {errorsByItem[0].index + 1}
							</span>
							<Button
								variant="outline"
								size="sm"
								onClick={() => onScrollToItem(errorsByItem[0].index)}
								className="text-red-700 border-red-300 hover:bg-red-100 hover:border-red-400"
							>
								Ir para primeiro erro
							</Button>
						</div>
					</div>
				)}
			</motion.div>
		</AnimatePresence>
	);
};
