import { ApiResponse } from "@/shared/types/requests/requests.type";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { xmlUploadRequest } from "../../api/requests/xml-upload";
import { IXmlUploadReturnDto } from "../../dtos/xml-upload-return.dto";
import { InventoryTransformer, ProductTransformer, StockMovementTransformer, XmlUploadDtoTransformer } from "../../lib/xml-to-form-data";
import { validateXml } from "../../lib/xml-validator";
import { ICreateStock } from "../../validators/create-stock.validator";

const createXmlUploadTransformer = (): XmlUploadDtoTransformer => {
	const productTransformer = new ProductTransformer();
	const stockMovementTransformer = new StockMovementTransformer(productTransformer);
	const inventoryTransformer = new InventoryTransformer(stockMovementTransformer);
	return new XmlUploadDtoTransformer(inventoryTransformer);
};

const handleUploadSuccess = async (
	data: ApiResponse<IXmlUploadReturnDto>,
	transformer: XmlUploadDtoTransformer,
	reset: (data: ICreateStock) => void
): Promise<void> => {
	if (data.success) {
		try {
			console.log(data.data);
			await new Promise(resolve => setTimeout(resolve, 500));
			const dataXML = transformer.transform(data.data);
			toast.dismiss();
			toast.success("XML enviado com sucesso");
			reset(dataXML);
		} catch (error) {
			toast.dismiss();
			toast.error("Erro ao processar XML", { description: (error as Error).message });
			throw error;
		}
	} else {
		toast.dismiss();
		toast.error(data.data.message);
		throw new Error(data.data.message);
	}
};

export const useUploadXml = ({ reset }: { reset: (data: ICreateStock) => void }) => {
	const xmlUploadTransformer = createXmlUploadTransformer();

	const xmlUploadMutation = useMutation({
		mutationKey: ["xml-upload"],
		mutationFn: async ({ fileXML }: { fileXML: File }) => await xmlUploadRequest({ fileXML }),
		onSuccess: data => {
			handleUploadSuccess(data, xmlUploadTransformer, reset);
		},
		onError: async error => {
			console.error("Erro no upload do XML", error);
			toast.dismiss();
			toast.error("Erro no upload do XML", { description: (error as Error).message });
		},
	});

	const uploadXml = async (file: File) => {
		try {
			await validateXml(file);
			xmlUploadMutation.mutate({ fileXML: file });
		} catch (error) {
			console.error("Erro na validação do XML:", error);
		}
	};

	return {
		uploadXml,
		data: xmlUploadMutation.data,
		isLoading: xmlUploadMutation.isPending,
		error: xmlUploadMutation.error,
	};
};
