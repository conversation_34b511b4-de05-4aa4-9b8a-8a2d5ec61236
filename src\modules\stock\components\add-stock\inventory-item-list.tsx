import { useImperativeHandle, forwardRef } from "react";
import { UseFieldArrayReturn, UseFormReturn } from "react-hook-form";
import { FaBoxesPacking } from "react-icons/fa6";
import { Plus, Sparkles } from "lucide-react";
import { motion } from "framer-motion";
import { ICreateStock } from "../../validators/create-stock.validator";
import { InventoryItem } from "./inventory-item";
import { FormItem, FormMessage } from "@/shared/components/ui/form";
import { useScrollToError } from "../../hooks/form/use-scroll-to-error.hook";
import { ErrorSummary } from "./error-summary";

interface InventoryItemsListProps {
	methodsForm: UseFormReturn<ICreateStock>;
	inventoryFieldArray: UseFieldArrayReturn<ICreateStock, "inventories", "id">;
}

export interface InventoryItemsListRef {
	scrollToFirstError: () => void;
}

export const InventoryItemsList = forwardRef<InventoryItemsListRef, InventoryItemsListProps>(({ methodsForm, inventoryFieldArray }, ref) => {
	const { errors } = methodsForm.formState;
	const { scrollToFirstError, scrollToItemError, setItemRef, errorSummary } = useScrollToError({
		errors,
		arrayFieldName: "inventories",
	});

	useImperativeHandle(ref, () => ({
		scrollToFirstError,
	}));

	return (
		<>
			{/* Resumo de erros */}
			<ErrorSummary
				totalErrors={errorSummary.totalErrors}
				itemsWithErrors={errorSummary.itemsWithErrors}
				errorsByItem={errorSummary.errorsByItem}
				onScrollToItem={scrollToItemError}
			/>

			<fieldset className="border-2 border-dashed rounded-[15px] border-gray-200 p-6 mb-6 bg-white shadow-sm">
				<legend className="flex items-center space-x-2 text-lg font-semibold text-gray-700 px-2">
					<FaBoxesPacking className="text-mainColor" />
					<span>Itens do Estoque</span>
				</legend>

				{inventoryFieldArray.fields.length === 0 ? (
					<div className="text-center py-12">
						<div className="p-4 bg-gray-100 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
							<FaBoxesPacking className="text-gray-400 text-2xl" />
						</div>
						<h4 className="text-lg font-medium text-gray-600 mb-2">Nenhum item adicionado</h4>
						<p className="text-gray-500 mb-6">Comece adicionando o primeiro item ao seu estoque</p>
					</div>
				) : (
					<div className="space-y-4 mb-6">
						{inventoryFieldArray.fields.map((field, index) => (
							<FormItem key={field.id} ref={setItemRef(index)}>
								<InventoryItem
									index={index}
									removeItem={() => inventoryFieldArray.remove(index)}
									isExistingIdProduct={Boolean(field?.stockMovement?.product?.id)}
									isExistingIdPackage={Boolean(field?.stockMovement?.product?.package?.id)}
									methodsForm={methodsForm}
								/>
								{errors.inventories?.[index] && (
									<div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
										<FormMessage className="text-red-600 font-medium">
											Este item possui campos obrigatórios não preenchidos. Verifique os campos destacados em vermelho.
										</FormMessage>
									</div>
								)}
							</FormItem>
						))}
					</div>
				)}

				<div className="flex justify-center">
					<motion.button
						type="button"
						whileHover={{ scale: 1.02 }}
						whileTap={{ scale: 0.98 }}
						onClick={() => {
							inventoryFieldArray.append({
								stockMovement: {
									description: "",
									product: {
										id: undefined,
										supplierCode: "",
										name: "",
										description: "",
										price: undefined,
										costPrice: undefined,
										barcode: "",
										ncm: "",
										categoryId: undefined,
										package: {
											id: undefined,
											code: "",
											name: "",
											barcode: "",
											quantityPerPackage: undefined,
										},
									},
									quantity: undefined,
								},
								expirationDate: "",
							});
						}}
						className="flex items-center gap-3 bg-gradient-to-r from-mainColor to-mainColor/90 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-200 hover:shadow-lg hover:shadow-mainColor/25 group"
					>
						<div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors">
							<Plus size={20} />
						</div>
						<span>Adicionar Novo Item</span>
						<Sparkles size={16} className="opacity-70 group-hover:opacity-100 transition-opacity" />
					</motion.button>
				</div>
			</fieldset>
		</>
	);
});

InventoryItemsList.displayName = "InventoryItemsList";
