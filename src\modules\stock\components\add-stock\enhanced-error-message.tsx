import { AlertCircle } from "lucide-react";
import React from "react";
import { motion, AnimatePresence } from "framer-motion";

interface EnhancedErrorMessageProps {
	error?: string;
	fieldName?: string;
	className?: string;
}

export const EnhancedErrorMessage: React.FC<EnhancedErrorMessageProps> = ({
	error,
	fieldName,
	className = "",
}) => {
	if (!error) return null;

	return (
		<AnimatePresence>
			<motion.div
				initial={{ opacity: 0, y: -5, height: 0 }}
				animate={{ opacity: 1, y: 0, height: "auto" }}
				exit={{ opacity: 0, y: -5, height: 0 }}
				transition={{ duration: 0.2 }}
				className={`flex items-start gap-2 mt-1 p-2 bg-red-50 border border-red-200 rounded-md ${className}`}
			>
				<AlertCircle className="w-3 h-3 text-red-500 mt-0.5 flex-shrink-0" />
				<div className="flex-1">
					<span className="text-xs text-red-600 font-medium">{error}</span>
					{fieldName && (
						<div className="text-xs text-red-500 mt-0.5 opacity-75">
							Campo: {fieldName}
						</div>
					)}
				</div>
			</motion.div>
		</AnimatePresence>
	);
};

interface FieldErrorWrapperProps {
	children: React.ReactNode;
	error?: string;
	fieldName?: string;
	hasError?: boolean;
}

export const FieldErrorWrapper: React.FC<FieldErrorWrapperProps> = ({
	children,
	error,
	fieldName,
	hasError = false,
}) => {
	return (
		<div className={`relative ${hasError ? "pb-1" : ""}`}>
			<div className={hasError ? "ring-2 ring-red-200 rounded-md" : ""}>
				{children}
			</div>
			<EnhancedErrorMessage error={error} fieldName={fieldName} />
		</div>
	);
};
