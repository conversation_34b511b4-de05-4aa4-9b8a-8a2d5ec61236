import { useRef, useCallback, useMemo } from "react";
import { FieldErrors, FieldValues } from "react-hook-form";

interface IUseScrollToErrorHook<T extends FieldValues> {
	errors: FieldErrors<T>;
	arrayFieldName: keyof T;
}

interface ErrorSummary {
	totalErrors: number;
	itemsWithErrors: number;
	errorsByItem: Array<{
		index: number;
		errorCount: number;
		errorFields: string[];
	}>;
	firstErrorIndex: number;
}

export function useScrollToError<T extends FieldValues>({ errors, arrayFieldName }: IUseScrollToErrorHook<T>) {
	const itemRefs = useRef<(HTMLElement | null)[]>([]);

	const errorSummary = useMemo((): ErrorSummary => {
		const arrayErrors = errors[arrayFieldName] as FieldErrors[] | undefined;
		if (!arrayErrors) {
			return {
				totalErrors: 0,
				itemsWithErrors: 0,
				errorsByItem: [],
				firstErrorIndex: -1,
			};
		}

		const errorsByItem: ErrorSummary["errorsByItem"] = [];
		let totalErrors = 0;
		let firstErrorIndex = -1;

		for (let i = 0; i < arrayErrors.length; i++) {
			const itemErrors = arrayErrors[i];
			if (itemErrors) {
				if (firstErrorIndex === -1) {
					firstErrorIndex = i;
				}

				const errorFields = getErrorFields(itemErrors);
				const errorCount = errorFields.length;

				errorsByItem.push({
					index: i,
					errorCount,
					errorFields,
				});

				totalErrors += errorCount;
			}
		}

		return {
			totalErrors,
			itemsWithErrors: errorsByItem.length,
			errorsByItem,
			firstErrorIndex,
		};
	}, [errors, arrayFieldName]);

	const findFirstErrorIndex = useCallback((): number => {
		return errorSummary.firstErrorIndex;
	}, [errorSummary.firstErrorIndex]);

	const scrollToFirstError = useCallback(() => {
		const firstErrorIndex = findFirstErrorIndex();
		if (firstErrorIndex !== -1 && itemRefs.current[firstErrorIndex]) {
			// Scroll com offset para considerar headers fixos
			const element = itemRefs.current[firstErrorIndex];
			if (element) {
				const elementRect = element.getBoundingClientRect();
				const offset = 100; // Offset para headers e espaçamento
				const targetPosition = elementRect.top + window.pageYOffset - offset;

				window.scrollTo({
					top: targetPosition,
					behavior: "smooth",
				});
			}
		}
	}, [findFirstErrorIndex]);

	const scrollToItemError = useCallback((itemIndex: number) => {
		if (itemRefs.current[itemIndex]) {
			const element = itemRefs.current[itemIndex];
			if (element) {
				const elementRect = element.getBoundingClientRect();
				const offset = 100;
				const targetPosition = elementRect.top + window.pageYOffset - offset;

				window.scrollTo({
					top: targetPosition,
					behavior: "smooth",
				});
			}
		}
	}, []);

	const setItemRef = useCallback(
		(index: number) => (el: HTMLElement | null) => {
			itemRefs.current[index] = el;
		},
		[]
	);

	return {
		scrollToFirstError,
		scrollToItemError,
		setItemRef,
		itemRefs,
		errorSummary,
	};
}

// Função auxiliar para extrair campos com erro de um objeto de erro
function getErrorFields(errorObj: any, prefix = ""): string[] {
	const fields: string[] = [];

	if (!errorObj || typeof errorObj !== "object") {
		return fields;
	}

	for (const [key, value] of Object.entries(errorObj)) {
		const fieldPath = prefix ? `${prefix}.${key}` : key;

		if (value && typeof value === "object") {
			if ("message" in value) {
				// É um erro direto
				fields.push(fieldPath);
			} else {
				// É um objeto aninhado, continua a busca
				fields.push(...getErrorFields(value, fieldPath));
			}
		}
	}

	return fields;
}
